/*
 * Created Date: 3/02/2024 16:01:53
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Thursday, 31st October 2024 10:13:45
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';

import '../../../ticket/ticket_flowchart.entity.dart';
import '../../../ticket/ticket_list.entity.dart';
import '../../../ticket/ticket_node.entity.dart';
import 'ticket_action.dart';

final class TicketActionBottomSheetEntity {
  const TicketActionBottomSheetEntity({
    required this.ticketEntity,
    required this.ticketNodeEntity,
    required this.ticketFlowChartEntity,
  });

  final TicketEntity ticketEntity;
  final TicketNodeEntity ticketNodeEntity;
  final TicketFlowChartEntity ticketFlowChartEntity;
}

extension _DriveAddFileExt on TicketAction {
  BottomSheetActionModel toBottomSheetActionModel(
      BuildContext context, TicketActionBottomSheetEntity entity,
      {String? rightIconPath}) {
    return BottomSheetActionModel(
      displayName: displayName(),
      iconAsset: asset,
      onClick: () {
        onClick.call(context, entity);
      },
      iconColor: GPColor.contentPrimary,
      textColor: GPColor.contentPrimary,
      rightIconPath: rightIconPath,
    );
  }

  Future onClick(
      BuildContext context, TicketActionBottomSheetEntity entity) async {
    Navigator.of(context).pop(
      TicketActionBottomSheetResult(
        entity: entity,
        action: this,
      ),
    );
  }
}

final class TicketActionBottomSheetResult {
  TicketActionBottomSheetResult({required this.entity, required this.action});

  final TicketActionBottomSheetEntity entity;
  final TicketAction action;
}

final class TicketActionBottomSheet extends StatelessWidget {
  const TicketActionBottomSheet({
    required this.entity,
    required this.actions,
    required this.canAddFollowerAllStep,
    super.key,
  });

  final TicketActionBottomSheetEntity entity;
  final List<TicketAction> actions;
  final bool canAddFollowerAllStep;

  @override
  Widget build(BuildContext context) {
    return BottomSheetActionWidget(
      data: actions.map(
        (e) {
          final rightIconPath = canAddFollowerAllStep &&
                  e == TicketAction.addFollower
              ? Assets
                  .PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_LINE15_CHEVRON_RIGHT_SVG
              : null;
          return e.toBottomSheetActionModel(context, entity,
              rightIconPath: rightIconPath);
        },
      ).toList(),
    );
  }
}
