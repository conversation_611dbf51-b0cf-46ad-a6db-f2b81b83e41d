import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_feat_ticket/data/data.dart';
import 'package:gp_feat_ticket/domain/domain.dart';
import 'package:gp_feat_ticket/mapper/ticket_mapper.dart';
import 'package:gp_feat_ticket/route/popup/add_follower/add_follower_option.dart';

import 'ticket_details_tab_event.dart';
import 'ticket_details_tab_state.dart';

typedef OnTicketDetailErrorCallback = void Function();

/// bloc quản lý tab detail của 1 ticket
final class TicketDetailsTabBloc
    extends CoreV2BaseBloc<TicketDetailsTabEvent, TicketDetailsTabState>
    with GPTicketMapperMixin, _VariableMixin {
  TicketDetailsTabBloc({
    this.onErrorCallback,
  }) : super(const TicketDetailsTabState()) {
    on<TicketGetDetailEvent>(_onTicketGetDetailEvent);
    on<TicketFlowChartEvent>(_onTicketFlowChartEvent);
    on<TicketNodeEvent>(_onTicketNodeEvent);
    on<TicketActionAssigneeEvent>(_onTicketActionAssigneeEvent);
    on<TicketActionAddFollowerEvent>(_onTicketActionAddFollowerEvent);
    on<TicketAdditionalRequestsEvent>(_onTicketAdditionalRequestsEvent);
    on<TicketGetActionPermissionsEvent>(_onTicketGetActionPermissionsEvent);
    on<TicketChangeNodeStatusEvent>(_onTicketChangeNodeStatusEvent);
    on<TicketSpamEvent>(_onTicketSpamEvent);
    on<TicketMoveToOnHoldEvent>(_onTicketMoveToOnHoldEvent);
    on<TicketGetOnHoldRequestEvent>(_onTicketGetOnHoldRequestEvent);
    on<TicketAcceptOnHoldRequestEvent>(_onTicketAcceptOnHoldRequestEvent);
    on<TicketRejectOnHoldRequestEvent>(_onTicketRejectOnHoldRequestEvent);
    on<TicketCancelOnHoldRequestEvent>(_onTicketCancelOnHoldRequestEvent);
    on<TicketGetAdditionalRequestsEvent>(_onTicketGetAdditionalRequestsEvent);
    on<TicketUnfollowTicketEvent>(_onTicketUnfollowTicketEvent);
    on<TicketDeleteTicketEvent>(_onTicketDeleteTicketEvent);
    on<TicketCloseTicketEvent>(_onTicketCloseEvent);
    on<TicketReopenTicketEvent>(_onTicketReopenEvent);
    on<TicketReviewEvent>(_onTicketReviewEvent);
    on<TicketCancelEvent>(_onTicketCancelEvent);
    on<TicketAddLabelEvent>(_onTicketAddLabelEvent);
    on<TicketBackStepEvent>(_onTicketBackStepEvent);
    on<TicketBackEndStepEvent>(_onTicketBackEndStepEvent);
    on<TicketGetAssigneesEvent>(_onTicketGetAssigneesEvent);
    on<TicketGetIsTicketAssigneeEvent>(_onTicketGetIsTicketAssigneeEvent);
  }

  String? ticketId;

  final OnTicketDetailErrorCallback? onErrorCallback;

  void _fetchDetails() {
    if (ticketId == null) {
      return;
    }

    add(TicketGetDetailEvent(id: ticketId!));
  }

  // TODO(toanNM): thêm condition, hiện đang lúc nào cũng retry
  Future _onRetry({
    required Object error,
  }) async {
    onErrorCallback?.call();

    if (error is DioException) {
      if (error.response?.statusCode == 412) {
        if (ticketId != null) {
          // add(TicketGetDetailEvent(id: ticketId!));
        }
      }
    }
  }

  FutureOr _onTicketGetDetailEvent(
    TicketGetDetailEvent event,
    Emitter<TicketDetailsTabState> emit,
  ) async {
    ticketId = event.id;

    return runCatching(
      handleLoading: true,
      action: () async {
        final response = await detailsUseCase.execute(
          TicketDetailsInput(
            ticketId: event.id,
          ),
        );

        final entity = convert<TicketListResponse, TicketEntity>(response.data);

        emit(TicketDetailsLoaded(entity: entity));
      },
      doOnError: (p0, stackTrace) {
        return _onRetry(error: p0);
      },
    );
  }

  FutureOr _onTicketFlowChartEvent(
    TicketFlowChartEvent event,
    Emitter<TicketDetailsTabState> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      action: () async {
        final response = await flowChartUseCase.execute(
          TicketFlowChartInput(
            ticketId: event.ticketId,
          ),
        );

        final entity = convert<TicketFlowChartResponse, TicketFlowChartEntity>(
            response.data);

        emit(TicketDetailsFlowChartLoaded(entity: entity));
      },
      doOnError: (p0, stackTrace) {
        return _onRetry(error: p0);
      },
    );
  }

  FutureOr _onTicketNodeEvent(
    TicketNodeEvent event,
    Emitter<TicketDetailsTabState> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      action: () async {
        final nodeId = event.nodeId;
        // assert(event.flowChartEntity.edges.length <= 1);

        final response = await nodeUseCase.execute(
          TicketNodeInput(
            ticketId: event.ticketEntity.id.toString(),
            nodeId: nodeId ?? '',
          ),
        );

        final entity =
            convert<TicketNodeResponse, TicketNodeEntity>(response.data);

        emit(
          TicketDetailsNodeLoaded(
            ticketEntity: event.ticketEntity,
            ticketFlowChartEntity: event.flowChartEntity,
            ticketNodeEntity: entity,
          ),
        );
      },
      doOnError: (p0, stackTrace) {
        return _onRetry(error: p0);
      },
    );
  }

  FutureOr _onTicketActionAssigneeEvent(
    TicketActionAssigneeEvent event,
    Emitter<TicketDetailsTabState> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      action: () async {
        await updateAsigneeUseCase.execute(
          TicketUpdateAssigneeInput(
            ticketId: event.ticketId,
            nodeId: event.nodeId,
            params: TicketUpdateAssigneeParams(
              assigneeId: event.assigneeId,
              currentAssigneeId: event.currentAssigneeId,
            ),
          ),
        );
        if (event.action == TicketAction.addHandler) {
          emit(const TicketAddHandlerSuccess());
        } else {
          emit(const TicketChangeHandlerSuccess());
        }

        _fetchDetails();
      },
      doOnError: (p0, stackTrace) {
        return _onRetry(error: p0);
      },
    );
  }

  FutureOr _onTicketActionAddFollowerEvent(
    TicketActionAddFollowerEvent event,
    Emitter<TicketDetailsTabState> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      action: () async {
        if (event.option == AddFollowerOption.currentStep) {
          await addAsigneeUseCase.execute(
          TicketAddFollowerInput(
            ticketId: event.ticketId,
            nodeId: event.nodeId,
            params: event.params,
          ),
        );
        } else {

        }

        emit(const TicketAddFollowerSuccess());
      },
      doOnError: (p0, stackTrace) {
        return _onRetry(error: p0);
      },
    );
  }

  FutureOr _onTicketAdditionalRequestsEvent(
    TicketAdditionalRequestsEvent event,
    Emitter<TicketDetailsTabState> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      action: () async {
        await additionalRequestsUseCase.execute(
          TicketAdditionalRequestInput(
            params: event.params,
          ),
        );

        emit(const TicketAdditionalRequestSuccess());
      },
      doOnError: (p0, stackTrace) {
        return _onRetry(error: p0);
      },
    );
  }

  FutureOr _onTicketGetActionPermissionsEvent(
    TicketGetActionPermissionsEvent event,
    Emitter<TicketDetailsTabState> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      action: () async {
        TicketActionEntity? ticketActionEntity =
            TicketPermission().getTicketActions(
          ticketEntity: event.ticketEntity,
          ticketNodeEntity: event.ticketNodeEntity,
          ticketFlowChartEntity: event.ticketFlowChartEntity,
          isTicketAssignee: event.isTicketAssignee,
          adminWorkflow: event.adminWorkflow,
        );

        emit(
          TicketGetActionPermissionsSuccess(
            ticketActionEntity: ticketActionEntity,
          ),
        );
      },
      doOnError: (p0, stackTrace) {
        return _onRetry(error: p0);
      },
    );
  }

  FutureOr _onTicketChangeNodeStatusEvent(
    TicketChangeNodeStatusEvent event,
    Emitter<TicketDetailsTabState> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      action: () async {
        await updateNodeStatusUseCase.execute(
          TicketUpdateNodeStatusInput(
            ticketId: event.ticketEntity.id.toString(),
            nodeId: event.ticketNodeEntity.id.toString(),
            params: TicketUpdateNodeStatusParams(
              status: event.nodeStatus,
            ),
          ),
        );

        await Future.delayed(const Duration(milliseconds: 1000));

        emit(
          TicketChangeNodeStatusSuccess(
            nodeStatus: event.nodeStatus,
          ),
        );
      },
      doOnError: (p0, stackTrace) {
        return _onRetry(error: p0);
      },
    );
  }

  FutureOr _onTicketSpamEvent(
    TicketSpamEvent event,
    Emitter<TicketDetailsTabState> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      action: () async {
        await spamUseCase.execute(
          TicketSpamInput(
            ticketId: event.ticketEntity.id.toString(),
            nodeId: event.ticketNodeEntity.id.toString(),
            params: event.params,
          ),
        );

        emit(const TicketSpamSuccess());
      },
      doOnError: (p0, stackTrace) {
        return _onRetry(error: p0);
      },
    );
  }

  FutureOr _onTicketMoveToOnHoldEvent(
    TicketMoveToOnHoldEvent event,
    Emitter<TicketDetailsTabState> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      action: () async {
        final response = await moveToOnHoldUseCase.execute(
          TicketMoveToOnHoldInput(
            ticketId: event.ticketEntity.id.toString(),
            nodeId: event.ticketNodeEntity.id.toString(),
            params: event.params,
          ),
        );

        final status = TicketStatus.values.firstWhereOrNull(
                (element) => element.value == response.data['status']) ??
            TicketStatus.unknown;

        emit(TicketMoveToOnHoldSuccess(ticketStatus: status));
      },
      doOnError: (p0, stackTrace) {
        return _onRetry(error: p0);
      },
    );
  }

  FutureOr _onTicketGetOnHoldRequestEvent(
    TicketGetOnHoldRequestEvent event,
    Emitter<TicketDetailsTabState> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      action: () async {
        final response = await getOnHoldUseCase.execute(
          TicketGetOnHoldInput(
            ticketId: event.ticketEntity.id.toString(),
            nodeId: event.ticketNodeEntity.id.toString(),
          ),
        );

        if (response.data != null && (response.data?.length ?? 0) >= 1) {
          final entity =
              convert<TicketOnHoldRequestResponse, TicketOnHoldRequestEntity>(
            response.data!.first,
          );

          emit(TicketGetOnHoldSuccess(ticketOnHoldRequestEntity: entity));
        }
      },
      doOnError: (p0, stackTrace) {
        return _onRetry(error: p0);
      },
    );
  }

  FutureOr _onTicketGetAdditionalRequestsEvent(
    TicketGetAdditionalRequestsEvent event,
    Emitter<TicketDetailsTabState> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      action: () async {
        final response = await getAdditionalRequestsUseCase.execute(
          TicketGetAdditionalRequestsInput(
            ticketId: event.ticketEntity.id.toString(),
            nodeId: event.ticketNodeEntity.id.toString(),
          ),
        );

        if (response.data != null) {
          final entity = convertList<TicketAdditionalRequestResponse,
              TicketAdditionalRequestEntity>(
            response.data!,
          );

          emit(
            TicketGetAdditionalRequestsSuccess(
              ticketAdditionalRequestEntity: entity,
            ),
          );
        }
      },
      doOnError: (p0, stackTrace) {
        return _onRetry(error: p0);
      },
    );
  }

  FutureOr _onTicketAcceptOnHoldRequestEvent(
    TicketAcceptOnHoldRequestEvent event,
    Emitter<TicketDetailsTabState> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      action: () async {
        await acceptOnHoldUseCase.execute(
          TicketAcceptOnHoldInput(
            ticketId: event.ticketEntity.id.toString(),
            nodeId: event.ticketNodeEntity.id.toString(),
            onHoldId: event.ticketOnHoldRequestEntity.id.toString(),
          ),
        );
        emit(const TicketAcceptOnHoldSuccess());
      },
      doOnError: (p0, stackTrace) {
        return _onRetry(error: p0);
      },
    );
  }

  FutureOr _onTicketRejectOnHoldRequestEvent(
    TicketRejectOnHoldRequestEvent event,
    Emitter<TicketDetailsTabState> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      action: () async {
        await rejectOnHoldUseCase.execute(
          TicketRejectOnHoldInput(
            ticketId: event.ticketEntity.id.toString(),
            nodeId: event.ticketNodeEntity.id.toString(),
            onHoldId: event.ticketOnHoldRequestEntity.id.toString(),
          ),
        );
        emit(const TicketRejectOnHoldSuccess());
      },
      doOnError: (p0, stackTrace) {
        return _onRetry(error: p0);
      },
    );
  }

  FutureOr _onTicketCancelOnHoldRequestEvent(
    TicketCancelOnHoldRequestEvent event,
    Emitter<TicketDetailsTabState> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      action: () async {
        await cancelOnHoldUseCase.execute(
          TicketCancelOnHoldInput(
            ticketId: event.ticketEntity.id.toString(),
            nodeId: event.ticketNodeEntity.id.toString(),
            onHoldId: event.ticketOnHoldRequestEntity.id.toString(),
          ),
        );
        emit(const TicketCancelOnHoldSuccess());
      },
      doOnError: (p0, stackTrace) {
        return _onRetry(error: p0);
      },
    );
  }

  FutureOr _onTicketUnfollowTicketEvent(
    TicketUnfollowTicketEvent event,
    Emitter<TicketDetailsTabState> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      action: () async {
        await unfollowTicketUseCase.execute(
          TicketUnfollowInput(
            ticketId: event.ticketEntity.id.toString(),
            nodeId: event.ticketNodeEntity.id.toString(),
          ),
        );
        emit(const TicketUnfollowTicketSuccess());
      },
      doOnError: (p0, stackTrace) {
        return _onRetry(error: p0);
      },
    );
  }

  FutureOr _onTicketDeleteTicketEvent(
    TicketDeleteTicketEvent event,
    Emitter<TicketDetailsTabState> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      action: () async {
        await deleteTicketUseCase.execute(
          TicketDeleteInput(
            ticketId: event.ticketEntity.id.toString(),
          ),
        );
        emit(const TicketDeleteTicketSuccess());
      },
      doOnError: (p0, stackTrace) {
        return _onRetry(error: p0);
      },
    );
  }

  FutureOr _onTicketCloseEvent(
    TicketCloseTicketEvent event,
    Emitter<TicketDetailsTabState> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      action: () async {
        await closeTicketUseCase.execute(
          TicketCloseInput(
            ticketId: event.ticketEntity.id.toString(),
            hasReview: event.hasReview,
          ),
        );
        emit(const TicketConfirmCloseTicketSuccess());
      },
      doOnError: (p0, stackTrace) {
        return _onRetry(error: p0);
      },
    );
  }

  FutureOr _onTicketReopenEvent(
    TicketReopenTicketEvent event,
    Emitter<TicketDetailsTabState> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      action: () async {
        await reopenUseCase.execute(
          TicketReopenInput(
            ticketId: event.ticketEntity.id.toString(),
            params: event.params,
          ),
        );

        emit(const TicketReopenTicketSuccess());
      },
      doOnError: (p0, stackTrace) {
        return _onRetry(error: p0);
      },
    );
  }

  FutureOr _onTicketReviewEvent(
    TicketReviewEvent event,
    Emitter<TicketDetailsTabState> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      action: () async {
        await reviewUseCase.execute(
          TicketReviewInput(
            ticketId: event.ticketEntity.id.toString(),
            params: event.params,
          ),
        );

        emit(const TicketReviewTicketSuccess());
      },
      doOnError: (p0, stackTrace) {
        return _onRetry(error: p0);
      },
    );
  }

  FutureOr _onTicketCancelEvent(
    TicketCancelEvent event,
    Emitter<TicketDetailsTabState> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      action: () async {
        await cancelUseCase.execute(
          TicketCancelInput(
            ticketId: event.ticketEntity.id.toString(),
            params: event.params,
          ),
        );

        emit(const TicketCancelTicketSuccess());
      },
      doOnError: (p0, stackTrace) {
        return _onRetry(error: p0);
      },
    );
  }

  FutureOr _onTicketAddLabelEvent(
    TicketAddLabelEvent event,
    Emitter<TicketDetailsTabState> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      action: () async {
        await addLabelUseCase.execute(
          TicketLabelInput(
            ticketId: event.ticketEntity.id.toString(),
            nodeId: event.ticketNodeEntity.id.toString(),
            params: event.params,
          ),
        );

        emit(const TicketAddLabelSuccess());
      },
      doOnError: (p0, stackTrace) {
        return _onRetry(error: p0);
      },
    );
  }

  FutureOr _onTicketBackStepEvent(
    TicketBackStepEvent event,
    Emitter<TicketDetailsTabState> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      action: () async {
        await redoPreviousUseCase.execute(
          TicketRedoPreviousInput(
            ticketId: event.ticketEntity.id.toString(),
            nodeId: event.ticketNodeEntity.id.toString(),
            params: TicketRedoPreviousParams(
              reason: event.reason,
              nodeIds: event.nodeIds,
              ticketId: event.ticketEntity.id,
              id: event.ticketNodeEntity.id,
            ),
          ),
        );

        emit(const TicketBackStepSuccess());
      },
      doOnError: (p0, stackTrace) {
        return _onRetry(error: p0);
      },
    );
  }

  FutureOr _onTicketBackEndStepEvent(
    TicketBackEndStepEvent event,
    Emitter<TicketDetailsTabState> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      action: () async {
        await redoEndNodeUseCase.execute(
          TicketRedoEndNodeInput(
            ticketId: event.ticketEntity.id.toString(),
            params: TicketRedoEndNodeParams(
              reason: event.reason,
              nodeIds: event.nodeIds,
            ),
          ),
        );

        emit(const TicketBackEndStepSuccess());
      },
      doOnError: (p0, stackTrace) {
        return _onRetry(error: p0);
      },
    );
  }

  FutureOr _onTicketGetAssigneesEvent(
    TicketGetAssigneesEvent event,
    Emitter<TicketDetailsTabState> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      action: () async {
        final List<String> users = [];
        final List<String> collabs = [];
        final List<String> departments = [];
        final List<String> roles = [];

        final allAssignees = event.assignees ?? [];
        for (var assignee in allAssignees) {
          if (assignee.type == TicketAssigneeType.user) {
            users.add(assignee.id.toString());
          } else if (assignee.type == TicketAssigneeType.collab) {
            collabs.add(assignee.id.toString());
          } else if (assignee.type == TicketAssigneeType.department) {
            departments.add(assignee.id.toString());
          } else if (assignee.type == TicketAssigneeType.role) {
            roles.add(assignee.id.toString());
          }
        }

        if (allAssignees.isEmpty) {
          if (event.type == TicketGetAssigneeType.assignee) {
            emit(const TicketGetAssigneesSuccess(
              assignees: [],
            ));
          } else if (event.type == TicketGetAssigneeType.adminWorkflow) {
            emit(const TicketGetAdminWorkflowSuccess(
              adminWorkflow: [],
            ));
          }
        } else {
          final response = await membersUseCase.execute(
            MembersInput(
              userIds: users,
              collabIds: collabs,
              departmentIds: departments,
              roleIds: roles,
            ),
          );

          if (event.type == TicketGetAssigneeType.assignee) {
            emit(TicketGetAssigneesSuccess(
              assignees: response.result,
            ));
          } else if (event.type == TicketGetAssigneeType.adminWorkflow) {
            emit(TicketGetAdminWorkflowSuccess(
              adminWorkflow: response.result,
            ));
          }
        }
      },
      doOnError: (p0, stackTrace) {
        return _onRetry(error: p0);
      },
    );
  }

  FutureOr _onTicketGetIsTicketAssigneeEvent(
    TicketGetIsTicketAssigneeEvent event,
    Emitter<TicketDetailsTabState> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      action: () async {
        final response = await checkIsTicketAssigneeUseCase.execute(
          CheckIsTicketAssigneeInput(
            ticketId: event.ticketEntity.id.toString(),
          ),
        );

        emit(TicketGetIsTicketAssigneeSuccess(
          isTicketAssignee: response.isAssignee,
          ticketEntity: event.ticketEntity,
          ticketFlowChartEntity: event.ticketFlowChartEntity,
          ticketNodeEntity: event.ticketNodeEntity,
        ));
      },
      doOnError: (p0, stackTrace) {
        return _onRetry(error: p0);
      },
    );
  }
}

mixin _VariableMixin {
  final TicketDetailsUseCase detailsUseCase = GetIt.I<TicketDetailsUseCase>();
  final TicketFlowChartUseCase flowChartUseCase =
      GetIt.I<TicketFlowChartUseCase>();
  final TicketNodeUseCase nodeUseCase = GetIt.I<TicketNodeUseCase>();
  final MembersUseCase membersUseCase = GetIt.I<MembersUseCase>();
  final CheckIsTicketAssigneeUseCase checkIsTicketAssigneeUseCase =
      GetIt.I<CheckIsTicketAssigneeUseCase>();

  // action usecase
  final TicketUpdateAsigneeUseCase updateAsigneeUseCase =
      GetIt.I<TicketUpdateAsigneeUseCase>();
  final TicketAddFollowerUseCase addAsigneeUseCase =
      GetIt.I<TicketAddFollowerUseCase>();
      final TicketAddFollowerAllStepUseCase addFollowerAllStepUseCase =
      GetIt.I<TicketAddFollowerAllStepUseCase>();
  final TicketGetAdditionalRequestsUseCase getAdditionalRequestsUseCase =
      GetIt.I<TicketGetAdditionalRequestsUseCase>();
  final TicketAdditionalRequestsUseCase additionalRequestsUseCase =
      GetIt.I<TicketAdditionalRequestsUseCase>();
  final TicketUpdateNodeStatusUseCase updateNodeStatusUseCase =
      GetIt.I<TicketUpdateNodeStatusUseCase>();
  final TicketSpamUseCase spamUseCase = GetIt.I<TicketSpamUseCase>();

  final TicketMoveToOnHoldUseCase moveToOnHoldUseCase =
      GetIt.I<TicketMoveToOnHoldUseCase>();
  final TicketGetOnHoldUseCase getOnHoldUseCase =
      GetIt.I<TicketGetOnHoldUseCase>();
  final TicketAcceptOnHoldUseCase acceptOnHoldUseCase =
      GetIt.I<TicketAcceptOnHoldUseCase>();
  final TicketRejectOnHoldUseCase rejectOnHoldUseCase =
      GetIt.I<TicketRejectOnHoldUseCase>();
  final TicketCancelOnHoldUseCase cancelOnHoldUseCase =
      GetIt.I<TicketCancelOnHoldUseCase>();
  final TicketUnfollowUseCase unfollowTicketUseCase =
      GetIt.I<TicketUnfollowUseCase>();
  final TicketDeleteUseCase deleteTicketUseCase =
      GetIt.I<TicketDeleteUseCase>();
  final TicketReopenUseCase reopenUseCase = GetIt.I<TicketReopenUseCase>();
  final TicketCloseUseCase closeTicketUseCase = GetIt.I<TicketCloseUseCase>();
  final TicketReviewUseCase reviewUseCase = GetIt.I<TicketReviewUseCase>();
  final TicketCancelUseCase cancelUseCase = GetIt.I<TicketCancelUseCase>();
  final TicketLabelUseCase addLabelUseCase = GetIt.I<TicketLabelUseCase>();
  final TicketRedoPreviousUseCase redoPreviousUseCase =
      GetIt.I<TicketRedoPreviousUseCase>();
  final TicketRedoEndNodeUseCase redoEndNodeUseCase =
      GetIt.I<TicketRedoEndNodeUseCase>();
}
