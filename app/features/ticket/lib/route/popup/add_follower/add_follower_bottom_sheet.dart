import 'package:diffutil_dart/diffutil.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_ticket/domain/entity/ticket/ticket_list.entity.dart';
import 'package:gp_feat_ticket/domain/entity/ticket/ticket_node.entity.dart';
import 'package:gp_shared/domain/entity/assignee/assignee.entity.dart';
import 'package:gp_shared/domain/entity/select_invite_options.entity.dart';
import 'package:gp_shared/widgets/member_picker/member_picker_result.dart';
import 'package:gp_shared/widgets/member_picker/member_picker_wrapper.dart';

import '../../../domain/entity/enums/ticket/action/ticket_action.popup.dart';
import '../../../presentation/create/form/widgets/picker/member_picker_params.dart';
import 'add_follower_option.dart';

class AddFollowerBottomSheet extends StatelessWidget {
  const AddFollowerBottomSheet({
    super.key,
    required this.entity,
  });

  final TicketActionBottomSheetEntity entity;

  Future<void> _onOptionTap(
      BuildContext context, AddFollowerOption option) async {
    // Get current followers to filter them out
    final List<AssigneeEntity?> assigneeEntities =
        entity.ticketNodeEntity.defaultFollowers?.map((e) => e.info).toList() ??
            [];
    assigneeEntities.removeWhere((e) => e == null);
    final assigneeEntitiesNotNull = List<AssigneeEntity>.from(assigneeEntities);
    final results = await _pickMembers(
      assigneeEntities: List<AssigneeEntity>.from(assigneeEntitiesNotNull),
    );

    final addAssignees = <AssigneeEntity>[];
    final removeAssignees = <AssigneeEntity>[];

    if (assigneeEntities.isNotEmpty || results.isNotEmpty) {
      final diffResult = calculateListDiff<AssigneeEntity>(
        assigneeEntitiesNotNull,
        results,
        detectMoves: true,
        equalityChecker: (o1, o2) => o1.id == o2.id,
      );

      final updateResults = diffResult.getUpdatesWithData();

      if (updateResults.isEmpty) return null;

      for (final updateResult in updateResults) {
        updateResult.when(
          insert: (pos, data) {
            addAssignees.add(data);

            assigneeEntities.add(data);
          },
          remove: (pos, data) {
            removeAssignees.add(data);

            assigneeEntities.remove(data);
          },
          change: (pos, p1, p2) {
            // doNothing
          },
          move: (from, to, data) {
            // doNothing
          },
        );
      }
    }

    final result = AddFollowerBottomSheetResult(
      option: option,
      addMembers: addAssignees,
      removeMembers: removeAssignees,
    );

    Get.back(result: result);

    // Open member picker
    // final MemberPickerResult<SelectMemberEntity>? result =
    //     await GetIt.I<MemberPickerWrapper>().pickMember(
    //   TicketMemberPickerParams.pickExistsMember(
    //     title: "Chọn người theo dõi",
    //     pickerMode: SelectInviteesOptionsMode.addOnly,
    //     filterOutMemberIds: currentFollowers,
    //     ignoreMe: false,
    //   ),
    // );
  }

  Future<List<AssigneeEntity>> _pickMembers({
    List<AssigneeEntity>? assigneeEntities,
  }) async {
    final MemberPickerResult<SelectMemberEntity>? result =
        await GetIt.I<MemberPickerWrapper>().pickMember(
      TicketMemberPickerParams.pickMember(
        pickerMode: SelectInviteesOptionsMode.selectNewWithValue,
        currentSelectMemberEntity:
            SelectMemberEntity(assigneeEntities: assigneeEntities),
        closeOverlayWhenBack: false,
      ),
    );

    final entities = result?.resultEntity?.assigneeEntities ?? [];
    logDebug('_pickMembers entities -> $entities');

    return entities;
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12),
            topRight: Radius.circular(12),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            _buildHeader(context),
            Divider(
              height: 1,
              thickness: 1,
              color: GPColor.bgSecondary,
            ),
            // Options
            _buildOptions(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      child: Row(
        children: [
          const SizedBox(width: 48), // Space for alignment
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 10),
              child: Text(
                LocaleKeys.ticket_details_add_follower_title.tr,
                textAlign: TextAlign.center,
                style: textStyle(GPTypography.headingMedium)?.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                  height: 1.5,
                  color: GPColor.contentPrimary,
                ),
              ),
            ),
          ),
          InkWell(
            onTap: () => Get.back(),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              child: SvgWidget(
                Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_LINE15_XMARK_SVG,
                color: GPColor.contentPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptions(BuildContext context) {
    return Container(
      color: GPColor.bgPrimary,
      child: Column(
        children: [
          // Current step option
          _buildOptionItem(
            context,
            AddFollowerOption.currentStep,
          ),
          // Divider
          Container(
            height: 1,
            margin: const EdgeInsets.only(left: 64),
            color: GPColor.lineTertiary,
          ),

          // All steps option
          _buildOptionItem(
            context,
            AddFollowerOption.allSteps,
          ),
        ],
      ),
    );
  }

  Widget _buildOptionItem(BuildContext context, AddFollowerOption option) {
    return InkWell(
      onTap: () => _onOptionTap(context, option),
      child: Container(
        padding: const EdgeInsets.fromLTRB(24, 20, 8, 20),
        child: Row(
          children: [
            // Icon
            SvgWidget(
              option.iconAsset,
              width: 24,
              height: 24,
              color: GPColor.contentPrimary,
            ),

            const SizedBox(width: 16),

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    option.displayName,
                    style: textStyle(GPTypography.headingMedium)?.copyWith(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                      height: 1.5,
                      color: GPColor.contentPrimary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    option.description,
                    style: textStyle(GPTypography.bodyLarge)?.copyWith(
                      fontWeight: FontWeight.w400,
                      fontSize: 16,
                      height: 1.5,
                      color: GPColor.contentSecondary,
                    ),
                  ),
                ],
              ),
            ),

            // Chevron right
            SvgWidget(
              Assets
                  .PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_LINE15_CHEVRON_RIGHT_SVG,
              width: 24,
              height: 24,
              color: GPColor.contentSecondary,
            ),
          ],
        ),
      ),
    );
  }
}
