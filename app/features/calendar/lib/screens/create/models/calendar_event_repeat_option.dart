import 'package:gp_core/core.dart';

import '../custom_repeat/model/repeat_by_model.dart';
import 'calendar_event_schedule_type.dart';

enum CalendarEventRepeatOptionEnum {
  noRepeat,

  //
  daily,
  weekly,
  weeklyMondayToFriday,
  monthly,

  // all day
  dailyAllDay,
  weeklyAllDay,
  weeklyAllDayMondayToFriday,
  monthlyAllDay,
  custom,
}

class CalendarEventRepeatOption {
  CalendarEventRepeatOptionEnum optionEnum;
  DateTime dateTime;

  /// rRule init giá trị khi sử dụng `displayText` -> `displayText`
  String _rRule = "";

  set rRule(String rRuleStr) {
    updateRRuleModel(rRuleStr);
  }

  String get rRule => _rRule;

  RRuleModel? _rRuleModel;

  RRuleModel? get rRuleModel => _rRuleModel;

  void updateRRuleModel(String rRuleStr) {
    _rRule = rRuleStr;
    if (rRuleStr.isNotEmpty) {
      _rRuleModel = RRuleModel(rRuleStr, startDate: dateTime);
    }
  }

  late String displayName;

  static final noRepeat = CalendarEventRepeatOption(
    dateTime: DateTime.now(),
    optionEnum: CalendarEventRepeatOptionEnum.noRepeat,
    rRuleStr: "",
  );

  static final options = [
    CalendarEventRepeatOptionEnum.daily,
    CalendarEventRepeatOptionEnum.weekly,
    CalendarEventRepeatOptionEnum.monthly,
    CalendarEventRepeatOptionEnum.weeklyMondayToFriday,
    CalendarEventRepeatOptionEnum.noRepeat,
    CalendarEventRepeatOptionEnum.custom,
  ];

  static final optionsAllday = [
    CalendarEventRepeatOptionEnum.dailyAllDay,
    CalendarEventRepeatOptionEnum.weeklyAllDay,
    CalendarEventRepeatOptionEnum.monthlyAllDay,
    CalendarEventRepeatOptionEnum.weeklyAllDayMondayToFriday,
    CalendarEventRepeatOptionEnum.noRepeat,
    CalendarEventRepeatOptionEnum.custom,
  ];

  CalendarEventRepeatOption({
    required this.optionEnum,
    required this.dateTime,
    String rRuleStr = "",
  }) {
    _init(rRuleStr);
  }

  factory CalendarEventRepeatOption.fromCalendarEventScheduleType(
    CalendarEventScheduleType type,
    List<dynamic> value,
    DateTime dateTime,
    bool allday,
    String? rRule,
  ) {
    CalendarEventRepeatOption cero = CalendarEventRepeatOption(
        optionEnum: CalendarEventRepeatOptionEnum.noRepeat,
        dateTime: dateTime,
        rRuleStr: rRule ?? "");
    cero.optionEnum = cero.getCeroEByRRule(
      type,
      value,
      dateTime,
      allday,
      rRule,
    );
    return cero;
  }

  void update(String rRuleStr, {DateTime? startDate}) {
    if (startDate != null) {
      dateTime = startDate;
    }

    _init(rRuleStr);
  }

  /// to display after user select type and select date/time
  void _init(String rRuleStr) {
    if (rRuleStr.isNotEmpty && rRuleStr.contains("FREQ=")) {
      rRule = rRuleStr;
      displayName = getDisplayName(dateTime) ??
          LocaleKeys.calendar_label_repeat_custom.tr;
    } else {
      /// e.g Thursday
      switch (optionEnum) {
        case CalendarEventRepeatOptionEnum.daily:
        case CalendarEventRepeatOptionEnum.dailyAllDay:
          displayName = LocaleKeys.calendar_label_repeat_daily.tr;
          rRule = "FREQ=DAILY;INTERVAL=1";
          break;

        case CalendarEventRepeatOptionEnum.weekly:
        case CalendarEventRepeatOptionEnum.weeklyAllDay:
          displayName = LocaleKeys.calendar_label_repeat_weekly.tr;
          rRule = "FREQ=WEEKLY;BYDAY=${dateTime.weekDayStr};INTERVAL=1";
          break;

        case CalendarEventRepeatOptionEnum.monthly:
        case CalendarEventRepeatOptionEnum.monthlyAllDay:
          displayName = LocaleKeys.calendar_label_repeat_monthly.tr;
          rRule = "FREQ=MONTHLY;BYMONTHDAY=${dateTime.day};INTERVAL=1";
          break;

        case CalendarEventRepeatOptionEnum.weeklyMondayToFriday:
        case CalendarEventRepeatOptionEnum.weeklyAllDayMondayToFriday:
          displayName =
              LocaleKeys.calendar_calendar_label_repeat_monday_to_friday.tr;
          rRule = "FREQ=WEEKLY;BYDAY=MO,TU,WE,TH,FR;INTERVAL=1";
          break;

        case CalendarEventRepeatOptionEnum.noRepeat:
          displayName = LocaleKeys.calendar_label_no_repeat.tr;
          rRule = "";
          break;

        case CalendarEventRepeatOptionEnum.custom:
          displayName = getDisplayName(dateTime) ??
              LocaleKeys.calendar_label_repeat_custom.tr;
          rRule = rRuleStr;
          break;
      }
    }
  }

  CalendarEventScheduleType getCESTByRRule() {
    return toCalendarEventScheduleType();
  }

  CalendarEventScheduleType toCalendarEventScheduleType() {
    switch (optionEnum) {
      case CalendarEventRepeatOptionEnum.daily:
      case CalendarEventRepeatOptionEnum.dailyAllDay:
        return CalendarEventScheduleType.daily;
      //
      case CalendarEventRepeatOptionEnum.weekly:
      case CalendarEventRepeatOptionEnum.weeklyMondayToFriday:
      case CalendarEventRepeatOptionEnum.weeklyAllDay:
      case CalendarEventRepeatOptionEnum.weeklyAllDayMondayToFriday:
        return CalendarEventScheduleType.weekly;
      //
      case CalendarEventRepeatOptionEnum.monthly:
      case CalendarEventRepeatOptionEnum.monthlyAllDay:
        return CalendarEventScheduleType.monthly;
      //
      // case CalendarEventRepeatOptionEnum.yearly:
      // case CalendarEventRepeatOptionEnum.yearlyAllDay:
      //   return CalendarEventScheduleType.monthly;
      //
      case CalendarEventRepeatOptionEnum.noRepeat:
        return CalendarEventScheduleType.once;
      case CalendarEventRepeatOptionEnum.custom:
        if (_rRuleModel == null) {
          return CalendarEventScheduleType.once;
        } else {
          return _rRuleModel!.getCest() ?? CalendarEventScheduleType.once;
        }
    }
  }
}

extension IsMondayToFridayValue on List {
  bool get isMondayToFridayValue {
    try {
      /// if event is monday to friday event
      /// the value = [1,2,3,4,5]
      return join('') == '12345';
    } catch (e) {
      logDebug(e);
      return false;
    }
  }
}

extension _CeroConverter on CalendarEventRepeatOption {
  /*
    Convert CalendarEventRepeatOptionEnum từ 1 recurrenceRule
  */
  CalendarEventRepeatOptionEnum getCeroE(
    CalendarEventScheduleType type,
    List<dynamic> value,
    DateTime dateTime,
    bool allday,
    String? rRule,
  ) {
    var optionEnum = CalendarEventRepeatOptionEnum.noRepeat;

    switch (type) {
      case CalendarEventScheduleType.once:
        optionEnum = CalendarEventRepeatOptionEnum.noRepeat;

        break;
      case CalendarEventScheduleType.daily:
        optionEnum = allday
            ? CalendarEventRepeatOptionEnum.dailyAllDay
            : CalendarEventRepeatOptionEnum.daily;
        break;
      case CalendarEventScheduleType.weekly:
        if (value.isMondayToFridayValue) {
          optionEnum = allday
              ? CalendarEventRepeatOptionEnum.weeklyAllDayMondayToFriday
              : optionEnum = CalendarEventRepeatOptionEnum.weeklyMondayToFriday;
        } else {
          optionEnum = allday
              ? CalendarEventRepeatOptionEnum.weeklyAllDay
              : CalendarEventRepeatOptionEnum.weekly;
        }
        break;
      case CalendarEventScheduleType.monthly:
        // Lặp vào ngày trong tháng
        optionEnum = allday
            ? CalendarEventRepeatOptionEnum.monthlyAllDay
            : CalendarEventRepeatOptionEnum.monthly;
        break;
      case CalendarEventScheduleType.yearly:
        optionEnum = CalendarEventRepeatOptionEnum.custom;
        break;
    }

    return optionEnum;
  }

  CalendarEventRepeatOptionEnum getCeroEByRRule(
    CalendarEventScheduleType type,
    List<dynamic> value,
    DateTime dateTime,
    bool allday,
    String? rRule,
  ) {
    if (rRule == null || rRule.isEmpty) {
      return CalendarEventRepeatOptionEnum.noRepeat;
    }

    return rRuleModel?.getCeroEByRRule(dateTime) ??
        getCeroE(
          type,
          value,
          dateTime,
          allday,
          rRule,
        );
  }

  String? getDisplayName(DateTime startDate) {
    return rRuleModel?.getDisplayName(startDate,
        defaultAddedDuration: const Duration());
  }
}
