# org.gradle.jvmargs=-Xmx8G -XX:MaxMetaspaceSize=4G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError
# android.useAndroidX=true
# android.enableJetifier=true
# org.gradle.parallel=true
#org.gradle.jvmargs=-Xmx4G -XX:MaxMetaspaceSize=2G -XX:+HeapDumpOnOutOfMemoryError
android.useAndroidX=true
android.enableJetifier=true
org.gradle.parallel=true
org.gradle.configureondemand=false
org.gradle.jvmargs=-Xmx4g -Dkotlin.daemon.jvm.options=-Xmx2g -Dfile.encoding=UTF-8 -Dkotlin.compiler.execution.strategy=in-process
kotlin.jvm.target=17
kotlin.jvm.target.validation.mode = IGNORE